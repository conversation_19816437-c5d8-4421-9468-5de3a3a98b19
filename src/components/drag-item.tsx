import { useSpring, animated } from '@react-spring/web';
import { useGesture } from '@use-gesture/react';
import { ReactNode, useEffect, useRef } from 'react';

export const DragItem = ({
  initialPosition = { x: 0, y: 0},
  onPositionChange,
  children,
}: {
  initialPosition: { x: number; y: number };
  onPositionChange?: (position: any) => void;
  children: ReactNode;
}) => {
  useEffect(() => {
    const preventDefault = (e: Event) => e.preventDefault();
    document.addEventListener('gesturestart', preventDefault);
    document.addEventListener('gesturechange', preventDefault);

    return () => {
      document.removeEventListener('gesturestart', preventDefault);
      document.removeEventListener('gesturechange', preventDefault);
    };
  }, []);
  const target = useRef(null);
  const [{ x, y }, api] = useSpring(() => ({
    ...initialPosition,
  }));

  useGesture(
    {
      onDrag: ({ offset: [x, y] }) => {
        // Prevent negative coordinates and ensure within screen bounds
        const boundedX = Math.max(0, Math.min(x, window.innerWidth - 64)); // 64px estimated button width
        const boundedY = Math.max(0, Math.min(y, window.innerHeight - 64)); // 64px estimated button height
        
        api({ x: boundedX, y: boundedY });
      },
      onDragEnd: ({ offset: [x, y] }) => {
        // Apply same bounds to final position
        const boundedX = Math.max(0, Math.min(x, window.innerWidth - 64));
        const boundedY = Math.max(0, Math.min(y, window.innerHeight - 64));
        
        // Determine which corner is closest and save it
        const corners = ['top-left', 'top-right', 'bottom-left', 'bottom-right'] as const;
        const centerX = window.innerWidth / 2;
        const centerY = window.innerHeight / 2;
        
        let closestCorner: typeof corners[number];
        if (boundedX < centerX && boundedY < centerY) closestCorner = 'top-left';
        else if (boundedX >= centerX && boundedY < centerY) closestCorner = 'top-right';
        else if (boundedX < centerX && boundedY >= centerY) closestCorner = 'bottom-left';
        else closestCorner = 'bottom-right';
        
        onPositionChange?.(closestCorner);
      }
    },
    { target, eventOptions: { passive: false } },
  );

  return (
    <>
      <animated.div
        ref={target}
        className="h-max w-max"
        style={{
          x,
          y,
          touchAction: 'none',
        }}
      >
        {children}
      </animated.div>
    </>
  );
};
