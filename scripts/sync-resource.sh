#!/usr/bin/bash

# Check exist and install rsync
if ! [ -x "$(command -v rsync)" ]; then
  apk add rsync
fi

HOME=$(pwd)

# Load env vars
. "${HOME}/scripts/load-env-vars.sh"

# Check existing of version of this release
#. "${HOME}/scripts/check-version-exist.sh"

# Variables
CDN_SRC="${HOME}/${BUILD_DIR}-cdn"
CDN_DESC="${CDN_USERNAME}@${CDN_SERVER_IP}:${CDN_SERVER_DIR}"

if [ -n "$CI_PIPELINE_SOURCE" ]; then
  CDN_DESC="${CDN_USERNAME}@${CDN_SERVER_IP}::${CDN_SERVER_DIR}"
fi

# Check existing of build dir
if [ ! -d "$BUILD_DIR" ]; then
  echo "ERROR: not found build directory ${BUILD_DIR}"
  exit 1
fi

mkdir -pv "${CDN_SRC}/${APP_NAME}/${npm_package_version}"
cp -a "${HOME}/${BUILD_DIR}/." "${CDN_SRC}/${APP_NAME}/${npm_package_version}"
if [ $? -eq 0 ]; then
  echo "SUCCESSFUL: Made the CDN temp dir success"
else
  echo "ERROR: Make the CDN temp dir error"
  exit 1
fi

# Sync resource to CDN server
echo "INFO: Syncing resource from ${CDN_SRC}/ to ${CDN_DESC} ..."
rsync -avrzc "${CDN_SRC}/" "${CDN_DESC}"
if [ $? -eq 0 ]; then
  echo "SUCCESSFUL: Sync resource done"
else
  echo "ERROR: Sync resource error"
  exit 1
fi

if [ "$TARGET_ENV" = "pro" ]; then
  echo "CDN_URL:${PUBLIC_URL}/remoteEntry.js"
  exit
fi
