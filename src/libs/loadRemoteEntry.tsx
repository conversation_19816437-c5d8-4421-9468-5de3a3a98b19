export function loadRemoteEntry(url: string) {
  return new Promise<void>((resolve, reject) => {
    const element = document.createElement('script');

    element.src = url;
    element.type = 'text/javascript';
    element.async = true;

    element.onload = () => {
      console.log(`Dynamic Script Loaded: ${url}`);
      resolve();
    };

    element.onerror = () => {
      console.error(`Dynamic Script Error: ${url}`);
      reject();
    };

    document.head.appendChild(element);
  });
}
