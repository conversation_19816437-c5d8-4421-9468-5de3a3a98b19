/** @type {import('tailwindcss').Config} */
const config = {
  content: ['./public/index.html', './src/**/*.{.html,js,ts,jsx,tsx}'],
  important: ".remote-dev-tools-root",
  theme: {
    extend: {
      fontSize: {
        xs: ['12px', '16px'],
        sm: ['14px', '18px'],
        base: ['16px', '20px'],
      },
      colors: {
        dark: {
          500: '#001F3E',
        },
        gray: {
          100: '#F2F4F5',
          200: '#CCD2D8',
          300: '#CCD2D8',
          900: '#001F3E',
          500: '#66798B',
        },
        blue: {
          100: '#EFF8FF',
          700: '#0033C9',
        },
      },
    },
  },
  plugins: [
    require('tailwindcss-react-aria-components'),
    require('tailwindcss-animate'),
  ],
  corePlugins: {
    preflight: false,
  },
};

export default config;
