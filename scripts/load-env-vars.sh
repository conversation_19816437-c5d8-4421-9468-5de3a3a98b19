#!/usr/bin/bash

HOME=$(pwd)
CONFIG_DIR="${HOME}/env"

REF_NAME=dev
VERSION="0.0.0-${REF_NAME}.${CI_PIPELINE_IID}"

npm version "${VERSION}" --allow-same-version --no-git-tag-version --no-commit-hooks


# Extract version value from package.json
if [ -z "$npm_package_version" ]; then
  npm_package_version=$(node -p "require('./package.json').version")
fi

# Load .env file
if [ -e "${CONFIG_DIR}/.env" ]; then
  . "${CONFIG_DIR}/.env"
  cat "${CONFIG_DIR}/.env" | tee -a $(pwd)/.env
fi

if [ ! -z "$CI_COMMIT_BRANCH" ]; then
  if [ "$CI_COMMIT_BRANCH" = "$DEVELOP_BRANCH" ] || [ "$CI_COMMIT_BRANCH" = "develop" ]; then
    # Load .env.develop file
    . "${CONFIG_DIR}/.env.develop"
    cat "${CONFIG_DIR}/.env.develop" | tee -a "$(pwd)/.env"
  elif [ "$CI_COMMIT_BRANCH" = "$STAGING_BRANCH" ] || [ "$CI_COMMIT_BRANCH" = "staging" ]; then
    # Load .env.staging file
    . "${CONFIG_DIR}/.env.staging"
    cat "${CONFIG_DIR}/.env.staging" | tee -a "$(pwd)/.env"
  elif [ "$CI_COMMIT_BRANCH" = "$PRODUCTION_BRANCH" ] || [ "$CI_COMMIT_BRANCH" = "master" ]; then
    echo "INFO: Load .env.production file"
    # Load .env.production file
    . "${CONFIG_DIR}/.env.production"
    cat "${CONFIG_DIR}/.env.production" | tee -a "$(pwd)/.env"
  elif [ "$CI_COMMIT_BRANCH" = "$TEST_CI" ]; then
    # Load .env.production file
    . "${CONFIG_DIR}/.env.develop"
    cat "${CONFIG_DIR}/.env.develop" | tee -a "$(pwd)/.env"
  fi
fi

export npm_package_version=$npm_package_version
export PUBLIC_URL=$PUBLIC_URL
export REACT_APP_ENV=$REACT_APP_ENV
export REACT_APP_COMMON_BASE_URL=$REACT_APP_COMMON_BASE_URL
export GENERATE_SOURCEMAP=$GENERATE_SOURCEMAP

export APP_ID=$APP_ID
export APP_NAME=$APP_NAME
export BUILD_DIR=$BUILD_DIR

export CONFIG_TOOL_API_URL=$CONFIG_TOOL_API_URL

export CDN_USERNAME=$CDN_USERNAME
export CDN_SERVER_IP=$CDN_SERVER_IP
export CDN_SERVER_DIR=$CDN_SERVER_DIR
export ESLINT_NO_DEV_ERRORS=true
