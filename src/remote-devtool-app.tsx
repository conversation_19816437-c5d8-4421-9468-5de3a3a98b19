import ReactDOM from 'react-dom/client';
import App from './app';

function createElement(id: string) {
  const ele = document.createElement('div');
  ele.id = id;
  document.body.append(ele);
}

function removeElement(id: string) {
  const ele = document.getElementById(id);
  ele && ele.remove();
}

export const bootstrap = () => {
  console.log('bootstrap');
  return Promise.resolve();
};

let root: ReactDOM.Root | undefined;
export const mount = () => {
  return new Promise<void>((resolve, reject) => {
    const idRoot = 'remote-devtool';
    let ele = document.getElementById(idRoot);
    if (!ele) {
      createElement(idRoot);
      ele = document.getElementById(idRoot);
    }

    root = ReactDOM.createRoot(ele!);
    root.render(<App />);
    resolve();
  });
};

export const unmount = () => {
  return new Promise<void>((resolve, reject) => {
    root && root.unmount();
    root = undefined;
    resolve();
  });
};
