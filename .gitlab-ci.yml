stages:
  - valid
  - install_dependencies
  - build
  - sync_resource
  - create_release

# pnpm setup
.pnpm_setup: &pnpm_setup
  - corepack enable
  - corepack prepare pnpm@latest-9 --activate
  - pnpm config set store-dir .pnpm-store

# # Auth private registry
# .auth_private: &auth_private
#   - |
#     {
#       echo "//repo.zalopay.vn/verdaccio/:_authToken=\${VERDACCIO_ZTOOL_TOKEN}"
#       echo "//repo.zalopay.vn:443/verdaccio/:_authToken=\${VERDACCIO_ZTOOL_TOKEN}"
#     } | tee -a .npmrc


default:
  image: registry-gitlab.zalopay.vn/docker/images/node:18-rsync
  before_script:
    - *pnpm_setup
    # - *auth_private

variables:
  PRODUCTION_BRANCH: 'production'
  STAGING_BRANCH: 'staging'
  DEVELOP_BRANCH: 'dev'
  TEST_CI: 'test-ci'
  TURBO_TELEMETRY_DISABLED: 1
  DO_NOT_TRACK: 1

check-version-exist:
  stage: valid
  script:
    # Execute check existing of version script
    - sh scripts/check-version-exist.sh
  rules:
    # Execute jobs when a commit to master|staging|develop branch
    - if: '$CI_PIPELINE_SOURCE == "push" && (
        $CI_COMMIT_BRANCH == $TEST_CI ||
        $CI_COMMIT_BRANCH == $DEVELOP_BRANCH ||
        $CI_COMMIT_BRANCH == $STAGING_BRANCH ||
        $CI_COMMIT_BRANCH == $PRODUCTION_BRANCH )'
  tags:
    - docker-executor-7118

install_dependencies:
  stage: install_dependencies
  script:
    # Install dependencies
    - pnpm install
  rules:
    # Execute jobs when a commit to master|staging|develop branch
    - if: '$CI_PIPELINE_SOURCE == "push" && (
        $CI_COMMIT_BRANCH == $TEST_CI ||
        $CI_COMMIT_BRANCH == $DEVELOP_BRANCH ||
        $CI_COMMIT_BRANCH == $STAGING_BRANCH ||
        $CI_COMMIT_BRANCH == $PRODUCTION_BRANCH )'
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - .pnpm-store/
      - node_modules/
    policy: pull-push
  tags:
    - docker-executor-7118

build:
  stage: build
  script:
    # Execute build resource script
    - sh scripts/build-resource.sh
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
    policy: pull
  artifacts:
    paths:
      - dist/
  rules:
    # Execute jobs when a commit to master|staging|develop branch
    - if: '$CI_PIPELINE_SOURCE == "push" && (
        $CI_COMMIT_BRANCH == $TEST_CI ||
        $CI_COMMIT_BRANCH == $DEVELOP_BRANCH ||
        $CI_COMMIT_BRANCH == $STAGING_BRANCH ||
        $CI_COMMIT_BRANCH == $PRODUCTION_BRANCH )'
  tags:
    - docker-executor-7118

sync_resource:
  stage: sync_resource
  script:
    # Execute sync resource script
    - sh scripts/sync-resource.sh
  artifacts:
    paths:
      -  dist/
  rules:
    # Execute jobs when a commit to master|staging|develop branch
    - if: '$CI_PIPELINE_SOURCE == "push" && (
        $CI_COMMIT_BRANCH == $TEST_CI ||
        $CI_COMMIT_BRANCH == $DEVELOP_BRANCH ||
        $CI_COMMIT_BRANCH == $STAGING_BRANCH ||
        $CI_COMMIT_BRANCH == $PRODUCTION_BRANCH )'
  tags:
    - docker-executor-7118

create_release:
  stage: create_release
  script:
    # Execute create release script
    - sh scripts/create-release.sh
  rules:
    # Execute jobs when a commit to master|staging|develop branch
    - if: '$CI_PIPELINE_SOURCE == "push" && (
        $CI_COMMIT_BRANCH == $TEST_CI ||
        $CI_COMMIT_BRANCH == $DEVELOP_BRANCH ||
        $CI_COMMIT_BRANCH == $STAGING_BRANCH ||
        $CI_COMMIT_BRANCH == $PRODUCTION_BRANCH )'
  tags:
    - docker-executor-7118
