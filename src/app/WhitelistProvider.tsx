
import { createContext, useContext, useEffect, useRef, useState, useCallback, ReactNode } from 'react';

declare global {
  interface Window {
    __USER_INFO__?: string;
  }
}

const cacheKey = window.crypto.getRandomValues(new Int16Array(1))[0] || 1;
const WHITE_LIST_URL = `https://simg.zalopay.com.vn/fs/dev-tools/white-list.json?cacheKey=${Math.abs(cacheKey)}`;
const WHITE_LIST_UID = `https://simg.zalopay.com.vn/fs/dev-tools/white-list-uid.json?cacheKey=${Math.abs(cacheKey)}`;

interface WhitelistContextValue {
  show: boolean;
}

const WhitelistContext = createContext<WhitelistContextValue | null>(null);

export function WhitelistProvider({ children }: { children: ReactNode }) {
  const [show, setShow] = useState(false);
  const whiteListPaths = useRef<string[]>([]);
  const whiteListApps = useRef<string[]>([]);
  const whiteListUIDs = useRef<string[]>([]);

  const checkEnableDevtool = useCallback(() => {
    const url = window.location.href;
    const isShow = whiteListPaths.current.some((path) => url.includes(path));
    setShow(!!isShow);
  }, []);

  const checkAppsEnableDevtool = useCallback((apps: string[]) => {
    const isShow = apps.some((app) => whiteListApps.current.includes(app));
    setShow(!!isShow);
  }, []);

  const checkUserEnableDevtool = useCallback(() => {
    const userID = window.__USER_INFO__
      ? JSON.parse(window.__USER_INFO__)?.zalopay_id
      : undefined;
    const isShow =
      userID && whiteListUIDs.current.some((uid) => uid === String(userID));
    setShow(!!isShow);
  }, []);

  const routingListener = useCallback((evt: CustomEvent) => {
    const apps = evt.detail?.appsByNewStatus.MOUNTED || [];
    if (apps && apps.length) {
      checkAppsEnableDevtool(apps);
      checkEnableDevtool();
      checkUserEnableDevtool();
    }
  }, [checkAppsEnableDevtool, checkEnableDevtool,checkUserEnableDevtool]);

  useEffect(() => {
    (async () => {
      try {
        const [whiteListResponse, whiteListUIDResponse] = await Promise.all([
          fetch(WHITE_LIST_URL),
          fetch(WHITE_LIST_UID),
        ]);

        const [whiteListData, whiteListUIDData] = await Promise.all([
          whiteListResponse.json(),
          whiteListUIDResponse.json(),
        ]);

        if (whiteListData?.white_list?.length) {
          whiteListPaths.current = whiteListData.white_list;
          checkEnableDevtool();
        }

        if (whiteListUIDData?.white_list_uid?.length) {
          whiteListUIDs.current = whiteListUIDData.white_list_uid;
          checkUserEnableDevtool();
        }
      } catch (error) {
        console.error('Failed to fetch whitelist data:', error);
      }
    })();
    window.addEventListener('single-spa:routing-event', routingListener as EventListener);
    return () =>
      window.removeEventListener('single-spa:routing-event', routingListener as EventListener);
  }, [routingListener, checkEnableDevtool, checkUserEnableDevtool]);
  console.log("show",show);
  return (
    <WhitelistContext.Provider value={{ show }}>
      {show ? children : null}
    </WhitelistContext.Provider>
  );
}

export function useWhitelist() {
  const context = useContext(WhitelistContext);
  if (!context) {
    throw new Error('useWhitelist must be used within a WhitelistProvider');
  }
  return context;
}

