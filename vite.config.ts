/// / <reference types="vitest" />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import federation from '@originjs/vite-plugin-federation';
import { join } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  build: {
    outDir: "./dist",
    assetsDir: ""
  },
  plugins: [
    react(),
    federation({
      name: 'remote_devtool',
    filename: 'remoteEntry.js',
    exposes: {
      './RemoteDevtool': join(__dirname, './src/remote-devtool-app'),
    },
    shared: [],
    }),
  ],
  envPrefix: 'REACT_APP',
  server: {
    host: '0.0.0.0',
    proxy: {
      '/v1/emvco/': 'https://zlpqc-ofp-emvco-gateway.zalopay.vn',
    },
  },
  resolve: {
    alias: {
      '~': '/src',
    },
  },
  // test: {
  //   exclude: [...configDefaults.exclude, '**/webpack-build/**'],
  //   coverage: {
  //     exclude: [
  //       ...coverageConfigDefaults.exclude,
  //       'webpack-build/**',
  //       'public/**',
  //       'mfconfig.*',
  //       '**/mocks/**',
  //       '**/{postcss,tailwind}.config.*',
  //     ],
  //   },
  //   environment: 'jsdom',
  //   setupFiles: ['./vitest.setup.ts'],
  // },
  // optimizeDeps: {
  //   exclude: ['js-big-decimal'],
  // },
});
